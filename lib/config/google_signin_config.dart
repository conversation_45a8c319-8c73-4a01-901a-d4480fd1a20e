// import 'package:flutter/foundation.dart';

// /// Configuration class for Google Sign-In Client IDs
// /// All Client IDs are from test-5c820 Firebase project
// class GoogleSignInConfig {
//   // Client ID for Android Debug/Development (SHA-1: 8ac73c0cffc21c28038497fb8e33f37b1d55d523)
//   static const String androidDebugClientId =
//       '545165014521-daav9q8d64aanct315ssfumat0fm5hir.apps.googleusercontent.com';

//   // Client ID for Android Release (SHA-1: a81efc57f5b0311996396040bdfea37ccd04cd8e)
//   static const String androidReleaseClientId =
//       '545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej.apps.googleusercontent.com';

//   // Client ID for Android Play Store (SHA-1: 3ced5a0a38312628bdc1af26b85ec284da66b467)
//   static const String androidPlayStoreClientId =
//       '545165014521-tk9u5kbu2sva7i4mni5g7h8bc2k5o6ej.apps.googleusercontent.com';

//   // Client ID for iOS (from test-5c820 project)
//   static const String iosClientId =
//       '545165014521-6lga42ejvda9e36l607gkllobevvpjfn.apps.googleusercontent.com';

//   // Client ID for Web (from test-5c820 project)
//   static const String webClientId =
//       '545165014521-j89kvjdhljgjkpi491km7qdu7rkgj4or.apps.googleusercontent.com';

//   // SHA-1 Fingerprints for reference
//   static const String debugSHA1 =
//       '8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23';
//   static const String releaseSHA1 =
//       'A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E';
//   static const String playStoreSHA1 =
//       '3C:ED:A5:0A:38:31:26:28:BD:C1:AF:26:B8:5E:C2:84:DA:66:B4:67'; // From Play Console - Updated for schoolx

//   /// Get the appropriate client ID based on the current platform and build mode
//   static String get currentPlatformClientId {
//     if (kIsWeb) {
//       return webClientId;
//     }

//     switch (defaultTargetPlatform) {
//       case TargetPlatform.android:
//         // Use debug client ID for debug builds, release for release builds
//         return kDebugMode ? androidDebugClientId : androidReleaseClientId;
//       case TargetPlatform.iOS:
//         return iosClientId;
//       default:
//         return androidDebugClientId; // fallback
//     }
//   }

//   /// Get Android client ID based on build mode
//   static String get androidClientId {
//     return kDebugMode ? androidDebugClientId : androidReleaseClientId;
//   }

//   /// Get specific client ID for Play Store (for manual testing)
//   static String get playStoreClientId {
//     return androidPlayStoreClientId;
//   }

//   /// Get all Android client IDs (for reference)
//   static List<String> get allAndroidClientIds {
//     return [
//       androidDebugClientId,
//       androidReleaseClientId,
//       androidPlayStoreClientId,
//     ];
//   }
// }
